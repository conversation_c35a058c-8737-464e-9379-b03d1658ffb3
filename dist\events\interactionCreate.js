"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InteractionCreateEventHandler = void 0;
const base_1 = require("./base");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const SuggestionService_1 = require("../services/suggestion/SuggestionService");
const electionButtonHandler_1 = require("../handlers/electionButtonHandler");
const pollButtonHandler_1 = require("../handlers/pollButtonHandler");
const leaderboardButtonHandler_1 = require("../handlers/leaderboardButtonHandler");
const helpButtonHandler_1 = require("../handlers/helpButtonHandler");
class InteractionCreateEventHandler extends base_1.BaseEventHandler {
    constructor(app) {
        super(app, 'interactionCreate');
        this.name = 'interactionCreate';
        this.electionButtonHandler = new electionButtonHandler_1.ElectionButtonHandler(app);
        this.pollButtonHandler = new pollButtonHandler_1.PollButtonHandler(app);
        this.leaderboardButtonHandler = new leaderboardButtonHandler_1.LeaderboardButtonHandler(app);
        this.suggestionService = new SuggestionService_1.SuggestionService(app);
    }
    async execute(interaction) {
        try {
            if (interaction.isChatInputCommand()) {
                await this.handleChatInputCommand(interaction);
            }
            else if (interaction.isButton()) {
                await this.handleButtonInteraction(interaction);
            }
            else if (interaction.isModalSubmit()) {
                await this.handleModalSubmit(interaction);
            }
        }
        catch (error) {
            this.handleError(error, {
                interactionType: interaction.type,
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
            });
        }
    }
    async handleChatInputCommand(interaction) {
        const command = this.app.client.commands.get(interaction.commandName);
        if (!command) {
            this.logger.warn(`[InteractionCreate] Unknown command: ${interaction.commandName}`);
            return;
        }
        try {
            this.logExecution(`Executing command: ${interaction.commandName}`, {
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
                channelId: interaction.channel?.id,
            });
            const context = {
                interaction,
                client: this.app.client,
                guild: interaction.guild,
                member: interaction.member,
                logger: this.logger,
            };
            await command.execute(interaction);
        }
        catch (error) {
            await (0, errorHandler_1.handleCommandError)(interaction, error);
        }
    }
    async handleButtonInteraction(interaction) {
        try {
            const { customId } = interaction;
            this.logExecution(`Handling button interaction: ${customId}`, {
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
                channelId: interaction.channel?.id,
            });
            const context = {
                interaction,
                client: this.app.client,
                guild: interaction.guild,
                member: interaction.member,
                logger: this.logger,
            };
            if (customId === 'quick_balance' || customId === 'salary_balance') {
                await this.handleQuickAction(interaction, 'balance');
            }
            else if (customId === 'quick_leaderboard' || customId === 'salary_leaderboard') {
                await this.handleQuickAction(interaction, 'leaderboard');
            }
            else if (customId === 'quick_roles' || customId === 'salary_roles') {
                await this.handleQuickAction(interaction, 'roles');
            }
            else if (customId.startsWith('buy_role_')) {
                await this.handleRoleAchievementInfo(interaction, customId);
            }
            else if (customId === 'announce_confirm') {
                await this.handleAnnouncementConfirm(interaction);
            }
            else if (customId === 'announce_cancel') {
                await this.handleAnnouncementCancel(interaction);
            }
            else if (customId.startsWith('help_')) {
                const timestamp = new Date().toISOString();
                console.log(`[INTERACTION DEBUG ${timestamp}] ===== ROUTING HELP BUTTON =====`);
                console.log(`[INTERACTION DEBUG ${timestamp}] CustomId: "${customId}"`);
                console.log(`[INTERACTION DEBUG ${timestamp}] User: ${interaction.user.tag} (${interaction.user.id})`);
                console.log(`[INTERACTION DEBUG ${timestamp}] Guild: ${interaction.guild?.name} (${interaction.guild?.id})`);
                console.log(`[INTERACTION DEBUG ${timestamp}] Routing to handleHelpButton...`);
                try {
                    await (0, helpButtonHandler_1.handleHelpButton)(interaction);
                    console.log(`[INTERACTION DEBUG ${timestamp}] ✅ handleHelpButton completed successfully`);
                }
                catch (error) {
                    console.error(`[INTERACTION DEBUG ${timestamp}] ❌ handleHelpButton failed:`, error);
                    throw error;
                }
            }
            else if (customId.startsWith('trade_')) {
                await this.handleTradeButton(interaction, customId);
            }
            else if (customId.startsWith('election_')) {
                await this.electionButtonHandler.handleElectionButton(interaction, customId);
            }
            else if (customId.startsWith('poll_')) {
                await this.pollButtonHandler.handlePollButton(interaction);
            }
            else if (customId.startsWith('leaderboard_')) {
                await this.leaderboardButtonHandler.handleLeaderboardButton(interaction, customId);
            }
            else if (customId.startsWith('suggestion_')) {
                await this.handleSuggestionButton(interaction, customId);
            }
            else {
                await interaction.reply({
                    content: 'This button interaction is not yet implemented.',
                    ephemeral: true
                });
            }
        }
        catch (error) {
            await (0, errorHandler_1.handleButtonError)(interaction, error);
        }
    }
    async handleModalSubmit(interaction) {
        try {
            const { customId } = interaction;
            if (customId.startsWith('suggestion_edit_modal_')) {
                await this.handleSuggestionEditModal(interaction, customId);
            }
            else {
                await interaction.reply({
                    content: 'This modal interaction is not yet implemented.',
                    ephemeral: true
                });
            }
        }
        catch (error) {
            this.logger.error('[InteractionCreate] Error handling modal submit', {
                error,
                customId: interaction.customId,
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
            });
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: 'An error occurred while processing your request.',
                    ephemeral: true
                });
            }
        }
    }
    async handleQuickAction(interaction, commandName) {
        const command = this.app.client.commands.get(commandName);
        if (command) {
            await command.execute(interaction);
        }
        else {
            await interaction.reply({
                content: `Command "${commandName}" not found.`,
                ephemeral: true
            });
        }
    }
    async handleRoleAchievementInfo(interaction, customId) {
        const roleId = customId.replace('buy_role_', '');
        await interaction.reply({
            content: `Role achievements are automatically unlocked when you reach the required PLC balance! Keep earning coins to unlock this achievement.`,
            ephemeral: true
        });
    }
    async handleAnnouncementConfirm(interaction) {
        const pendingAnnouncements = global.pendingAnnouncements;
        if (!pendingAnnouncements) {
            await interaction.reply({
                content: 'No pending announcements found. Please try the command again.',
                ephemeral: true
            });
            return;
        }
        const originalInteractionId = interaction.message?.interaction?.id;
        const announcementData = pendingAnnouncements.get(originalInteractionId);
        if (!announcementData) {
            await interaction.reply({
                content: 'Announcement data not found or expired. Please try the command again.',
                ephemeral: true
            });
            return;
        }
        pendingAnnouncements.delete(originalInteractionId);
        await interaction.deferUpdate();
        const announceModule = require('../commands/announce');
        await announceModule.processAnnouncement(interaction, announcementData);
    }
    async handleAnnouncementCancel(interaction) {
        const pendingAnnouncements = global.pendingAnnouncements;
        const originalInteractionId = interaction.message?.interaction?.id;
        if (pendingAnnouncements && originalInteractionId) {
            pendingAnnouncements.delete(originalInteractionId);
        }
        await interaction.update({
            content: `${embedBuilder_1.EMOJIS.ADMIN.WARNING} Announcement cancelled.`,
            embeds: [],
            components: []
        });
    }
    async handleTradeButton(interaction, customId) {
        try {
            const tradeService = this.app.getService('TradeService');
            const parts = customId.split('_');
            if (parts.length < 3) {
                throw new errorHandler_1.ValidationError('Invalid trade button format');
            }
            const action = parts[1];
            const tradeId = parts.slice(2).join('_');
            const trade = await tradeService.getTrade(tradeId);
            if (!trade) {
                throw new errorHandler_1.ValidationError('Trade not found');
            }
            if (!trade.involvesUser(interaction.user.id)) {
                throw new errorHandler_1.ValidationError('You are not a party to this trade');
            }
            switch (action) {
                case 'accept':
                    await this.handleTradeAccept(interaction, trade, tradeService);
                    break;
                case 'decline':
                    await this.handleTradeDecline(interaction, trade, tradeService);
                    break;
                case 'confirm':
                    await this.handleTradeConfirm(interaction, trade, tradeService);
                    break;
                case 'dispute':
                    await this.handleTradeDispute(interaction, trade, tradeService);
                    break;
                case 'cancel':
                    await this.handleTradeCancel(interaction, trade, tradeService);
                    break;
                case 'details':
                    await this.handleTradeDetails(interaction, trade);
                    break;
                default:
                    throw new errorHandler_1.ValidationError(`Unknown trade action: ${action}`);
            }
        }
        catch (error) {
            this.logger.error('Error handling trade button', { error, customId, userId: interaction.user.id });
            const embed = (0, embedBuilder_1.createErrorEmbed)('Trade Action Failed', error instanceof errorHandler_1.ValidationError ? error.message : 'An unexpected error occurred');
            if (interaction.replied || interaction.deferred) {
                await interaction.followUp({ embeds: [embed], ephemeral: true });
            }
            else {
                await interaction.reply({ embeds: [embed], ephemeral: true });
            }
        }
    }
    async handleTradeAccept(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        const updatedTrade = await tradeService.acceptTrade(trade.tradeId, interaction.user.id, interaction.client);
        await interaction.editReply({
            content: `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Trade Accepted!**\n\nThe trade has been activated and funds have been locked in escrow. You will receive further instructions via DM.`
        });
    }
    async handleTradeDecline(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        await tradeService.cancelTrade(trade.tradeId, interaction.user.id, 'Trade declined', interaction.client);
        await interaction.editReply({
            content: `${embedBuilder_1.EMOJIS.TRADE.CANCELLED} **Trade Declined**\n\nThe trade proposal has been declined and cancelled.`
        });
    }
    async handleTradeConfirm(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        const result = await tradeService.confirmTrade(trade.tradeId, interaction.user.id, interaction.client);
        if (result.completed) {
            await interaction.editReply({
                content: `${embedBuilder_1.EMOJIS.SUCCESS.PARTY} **Trade Completed!**\n\nBoth parties have confirmed completion. Funds have been released to the seller.`
            });
        }
        else {
            await interaction.editReply({
                content: `${embedBuilder_1.EMOJIS.SUCCESS.CHECK} **Confirmation Recorded**\n\nYour confirmation has been recorded. Waiting for the other party to confirm.`
            });
        }
    }
    async handleTradeDispute(interaction, trade, tradeService) {
        await interaction.reply({
            content: `${embedBuilder_1.EMOJIS.TRADE.DISPUTED} **Dispute System**\n\nThe dispute system will be implemented in the next phase. For now, please contact an administrator for assistance.`,
            ephemeral: true
        });
    }
    async handleTradeCancel(interaction, trade, tradeService) {
        await interaction.deferReply({ ephemeral: true });
        await tradeService.cancelTrade(trade.tradeId, interaction.user.id, 'Cancelled by user', interaction.client);
        await interaction.editReply({
            content: `${embedBuilder_1.EMOJIS.TRADE.CANCELLED} **Trade Cancelled**\n\nThe trade has been cancelled and any escrowed funds have been refunded.`
        });
    }
    async handleTradeDetails(interaction, trade) {
        await interaction.reply({
            content: `${embedBuilder_1.EMOJIS.MISC.MAGNIFYING} **Trade Details**\n\nDetailed trade view will be implemented in the next phase.`,
            ephemeral: true
        });
    }
    async handleSuggestionButton(interaction, customId) {
        try {
            const parts = customId.split('_');
            const action = parts[1];
            const suggestionId = parts[2];
            const authorId = parts[3];
            switch (action) {
                case 'upvote':
                    await this.handleSuggestionVote(interaction, suggestionId, 'upvote');
                    break;
                case 'downvote':
                    await this.handleSuggestionVote(interaction, suggestionId, 'downvote');
                    break;
                case 'edit':
                    await this.handleSuggestionEdit(interaction, suggestionId, authorId);
                    break;
                case 'delete':
                    await this.handleSuggestionDelete(interaction, suggestionId, authorId);
                    break;
                default:
                    await interaction.reply({
                        content: 'Unknown suggestion action.',
                        ephemeral: true
                    });
            }
        }
        catch (error) {
            this.logger.error('[InteractionCreate] Error handling suggestion button', {
                error,
                customId,
                userId: interaction.user.id,
                guildId: interaction.guild?.id,
            });
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: 'An error occurred while processing your request.',
                    ephemeral: true
                });
            }
        }
    }
    async handleSuggestionVote(interaction, suggestionId, voteType) {
        try {
            const result = await this.suggestionService.voteOnSuggestion(suggestionId, interaction.user.id, voteType, this.app.client);
            if (result.success) {
                const emoji = voteType === 'upvote' ? '🔺' : '🔻';
                const action = result.previousVote === voteType ? 'removed' :
                    result.previousVote ? 'changed to' : 'added';
                await interaction.reply({
                    content: `${emoji} ${action === 'removed' ? 'Vote removed!' : `${action} ${voteType}!`}\n` +
                        `**Upvotes:** ${result.upvoteCount} | **Downvotes:** ${result.downvoteCount}`,
                    ephemeral: true
                });
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                await interaction.reply({
                    content: `❌ ${error.message}`,
                    ephemeral: true
                });
            }
            else {
                throw error;
            }
        }
    }
    async handleSuggestionEdit(interaction, suggestionId, authorId) {
        if (interaction.user.id !== authorId) {
            await interaction.reply({
                content: '❌ You can only edit your own suggestions.',
                ephemeral: true
            });
            return;
        }
        const suggestion = await this.suggestionService.getSuggestion(suggestionId);
        if (!suggestion) {
            await interaction.reply({
                content: '❌ Suggestion not found.',
                ephemeral: true
            });
            return;
        }
        const { ModalBuilder, TextInputBuilder, TextInputStyle, ActionRowBuilder } = require('discord.js');
        const modal = new ModalBuilder()
            .setCustomId(`suggestion_edit_modal_${suggestionId}`)
            .setTitle('Edit Suggestion');
        const textInput = new TextInputBuilder()
            .setCustomId('suggestion_content')
            .setLabel('Suggestion Content')
            .setStyle(TextInputStyle.Paragraph)
            .setValue(suggestion.content)
            .setMaxLength(2000)
            .setMinLength(10)
            .setRequired(true);
        const actionRow = new ActionRowBuilder().addComponents(textInput);
        modal.addComponents(actionRow);
        await interaction.showModal(modal);
    }
    async handleSuggestionDelete(interaction, suggestionId, authorId) {
        try {
            const canDelete = await this.suggestionService.canUserDeleteSuggestion(suggestionId, interaction.user.id, interaction.member);
            if (!canDelete) {
                await interaction.reply({
                    content: '❌ You can only delete your own suggestions or you need Administrator permission.',
                    ephemeral: true
                });
                return;
            }
            const success = await this.suggestionService.deleteSuggestion(suggestionId, this.app.client);
            if (success) {
                const isAuthor = interaction.user.id === authorId;
                const message = isAuthor
                    ? '🗑️ Your suggestion has been deleted successfully.'
                    : '🗑️ Suggestion deleted successfully by administrator.';
                await interaction.reply({
                    content: message,
                    ephemeral: true
                });
                if (!isAuthor) {
                    this.logger.info('[InteractionCreate] Admin deleted suggestion', {
                        suggestionId,
                        adminId: interaction.user.id,
                        originalAuthorId: authorId,
                        guildId: interaction.guild?.id
                    });
                }
            }
            else {
                await interaction.reply({
                    content: '❌ Failed to delete suggestion.',
                    ephemeral: true
                });
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                await interaction.reply({
                    content: `❌ ${error.message}`,
                    ephemeral: true
                });
            }
            else {
                throw error;
            }
        }
    }
    async handleSuggestionEditModal(interaction, customId) {
        try {
            const suggestionId = customId.replace('suggestion_edit_modal_', '');
            const newContent = interaction.fields.getTextInputValue('suggestion_content');
            if (!newContent || newContent.trim().length < 10) {
                await interaction.reply({
                    content: '❌ Suggestions must be at least 10 characters long.',
                    ephemeral: true
                });
                return;
            }
            if (newContent.length > 2000) {
                await interaction.reply({
                    content: '❌ Suggestions cannot exceed 2000 characters.',
                    ephemeral: true
                });
                return;
            }
            const updatedSuggestion = await this.suggestionService.editSuggestion(suggestionId, newContent.trim(), this.app.client);
            if (updatedSuggestion) {
                await interaction.reply({
                    content: '✏️ Suggestion updated successfully!',
                    ephemeral: true
                });
            }
            else {
                await interaction.reply({
                    content: '❌ Failed to update suggestion.',
                    ephemeral: true
                });
            }
        }
        catch (error) {
            if (error instanceof errorHandler_1.ValidationError) {
                await interaction.reply({
                    content: `❌ ${error.message}`,
                    ephemeral: true
                });
            }
            else {
                throw error;
            }
        }
    }
}
exports.InteractionCreateEventHandler = InteractionCreateEventHandler;
