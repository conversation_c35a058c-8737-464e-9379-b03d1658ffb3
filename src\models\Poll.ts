import { Schema, model, Document } from 'mongoose';

/**
 * Poll Vote Interface
 */
export interface IPollVote {
  userId: string;
  optionIndex: number;
  voteWeight: number;
  voterDisplayName: string;
  voterUsername: string;
  timestamp: Date;
}

/**
 * Poll Interface
 */
export interface IPoll extends Document {
  // Core Poll Information
  pollId: string; // Unique identifier for the poll (UUID)
  guildId: string; // Discord guild ID
  channelId: string; // Channel where poll was created
  messageId: string; // Discord message ID of the poll embed
  
  // Poll Configuration
  title: string; // Poll title/question
  description: string; // Poll description/context
  createdBy: string; // Discord ID of user who created the poll
  
  // Role Configuration - Following suggestion system pattern
  eligibleVoterRoles: string[]; // Role IDs that can vote (with validation)
  
  // Poll Options
  options: string[]; // Array of poll options (2-20 options)
  
  // Voting Data
  votes: IPollVote[]; // Array of votes
  totalVotes: number; // Total number of individual votes
  totalVoteWeight: number; // Total coin weight of all votes
  
  // Poll State
  status: 'ACTIVE' | 'ENDED' | 'CANCELLED';
  
  // Timestamps
  createdAt: Date;
  endedAt?: Date;
  endedBy?: string; // User ID who ended the poll
  expiresAt: Date; // TTL field for automatic cleanup after 24 hours

  // Methods
  addVote(vote: IPollVote): void;
  removeVote(userId: string): boolean;
  endPoll(endedBy: string): void;
  getVoteResults(): Array<{
    votes: number;
    weight: number;
    percentage: number;
    weightPercentage: number;
  }>;
}

/**
 * Poll Vote Schema
 */
const pollVoteSchema = new Schema<IPollVote>({
  userId: {
    type: String,
    required: true,
    validate: {
      validator: function(v: string): boolean {
        return /^\d{17,20}$/.test(v);
      },
      message: 'User ID must be a valid Discord snowflake'
    }
  },
  optionIndex: {
    type: Number,
    required: true,
    min: [0, 'Option index cannot be negative'],
    max: [19, 'Option index cannot exceed 19']
  },
  voteWeight: {
    type: Number,
    required: true,
    min: [0, 'Vote weight cannot be negative']
  },
  voterDisplayName: {
    type: String,
    required: true,
    maxlength: [100, 'Display name cannot exceed 100 characters']
  },
  voterUsername: {
    type: String,
    required: true,
    maxlength: [50, 'Username cannot exceed 50 characters']
  },
  timestamp: {
    type: Date,
    default: Date.now
  }
}, { _id: false });

/**
 * Poll Schema
 */
const pollSchema = new Schema<IPoll>({
  pollId: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  guildId: {
    type: String,
    required: true,
    index: true,
    validate: {
      validator: function(v: string): boolean {
        return /^\d{17,20}$/.test(v);
      },
      message: 'Guild ID must be a valid Discord snowflake'
    }
  },
  channelId: {
    type: String,
    required: true,
    validate: {
      validator: function(v: string): boolean {
        return /^\d{17,20}$/.test(v);
      },
      message: 'Channel ID must be a valid Discord snowflake'
    }
  },
  messageId: {
    type: String,
    required: true,
    validate: {
      validator: function(v: string): boolean {
        return /^\d{17,20}$/.test(v);
      },
      message: 'Message ID must be a valid Discord snowflake'
    }
  },
  title: {
    type: String,
    required: true,
    maxlength: [256, 'Poll title cannot exceed 256 characters'],
    minlength: [1, 'Poll title cannot be empty']
  },
  description: {
    type: String,
    required: true,
    maxlength: [1024, 'Poll description cannot exceed 1024 characters'],
    minlength: [1, 'Poll description cannot be empty']
  },
  createdBy: {
    type: String,
    required: true,
    index: true,
    validate: {
      validator: function(v: string): boolean {
        return /^\d{17,20}$/.test(v);
      },
      message: 'Creator ID must be a valid Discord snowflake'
    }
  },
  eligibleVoterRoles: [{
    type: String,
    required: true,
    validate: {
      validator: function(v: string): boolean {
        return /^\d{17,20}$/.test(v);
      },
      message: 'Role ID must be a valid Discord snowflake'
    }
  }],
  options: [{
    type: String,
    required: true,
    maxlength: [100, 'Poll option cannot exceed 100 characters'],
    minlength: [1, 'Poll option cannot be empty']
  }],
  votes: [pollVoteSchema],
  totalVotes: {
    type: Number,
    default: 0,
    min: [0, 'Total votes cannot be negative']
  },
  totalVoteWeight: {
    type: Number,
    default: 0,
    min: [0, 'Total vote weight cannot be negative']
  },
  status: {
    type: String,
    enum: ['ACTIVE', 'ENDED', 'CANCELLED'],
    default: 'ACTIVE',
    index: true
  },
  createdAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  endedAt: {
    type: Date,
    index: true
  },
  endedBy: {
    type: String,
    validate: {
      validator: function(v: string): boolean {
        return !v || /^\d{17,20}$/.test(v);
      },
      message: 'Ended by ID must be a valid Discord snowflake'
    }
  },
  expiresAt: {
    type: Date,
    default: () => new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now
    index: { expireAfterSeconds: 0 } // TTL index for automatic cleanup
  }
}, {
  timestamps: false // Using custom timestamp fields
});

// Compound indexes for efficient queries
pollSchema.index({ guildId: 1, status: 1 });
pollSchema.index({ guildId: 1, createdBy: 1 });
pollSchema.index({ guildId: 1, createdAt: -1 });
pollSchema.index({ status: 1, expiresAt: 1 });

// Validation for arrays
pollSchema.pre('validate', function() {
  if (this.eligibleVoterRoles && this.eligibleVoterRoles.length === 0) {
    this.invalidate('eligibleVoterRoles', 'At least one eligible voter role is required');
  }
  
  if (this.options && (this.options.length < 2 || this.options.length > 20)) {
    this.invalidate('options', 'Poll must have between 2 and 20 options');
  }
});

// Methods for poll operations
pollSchema.methods.addVote = function(vote: IPollVote) {
  // Remove existing vote from same user if exists
  this.votes = this.votes.filter((v: IPollVote) => v.userId !== vote.userId);

  // Add new vote
  this.votes.push(vote);

  // Update totals
  this.totalVotes = this.votes.length;
  this.totalVoteWeight = this.votes.reduce((sum: number, v: IPollVote) => sum + v.voteWeight, 0);
};

pollSchema.methods.removeVote = function(userId: string) {
  const initialLength = this.votes.length;
  this.votes = this.votes.filter((v: IPollVote) => v.userId !== userId);

  // Update totals if vote was removed
  if (this.votes.length < initialLength) {
    this.totalVotes = this.votes.length;
    this.totalVoteWeight = this.votes.reduce((sum: number, v: IPollVote) => sum + v.voteWeight, 0);
    return true;
  }
  return false;
};

pollSchema.methods.endPoll = function(endedBy: string) {
  this.status = 'ENDED';
  this.endedAt = new Date();
  this.endedBy = endedBy;
};

pollSchema.methods.getVoteResults = function() {
  const results = new Array(this.options.length).fill(0).map(() => ({
    votes: 0,
    weight: 0,
    percentage: 0,
    weightPercentage: 0
  }));

  // Calculate vote counts and weights per option
  this.votes.forEach((vote: IPollVote) => {
    if (vote.optionIndex >= 0 && vote.optionIndex < results.length) {
      results[vote.optionIndex].votes++;
      results[vote.optionIndex].weight += vote.voteWeight;
    }
  });

  // Calculate percentages
  results.forEach(result => {
    result.percentage = this.totalVotes > 0 ? (result.votes / this.totalVotes) * 100 : 0;
    result.weightPercentage = this.totalVoteWeight > 0 ? (result.weight / this.totalVoteWeight) * 100 : 0;
  });

  return results;
};

export const Poll = model<IPoll>('Poll', pollSchema);
export default Poll;
