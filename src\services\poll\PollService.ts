import { GuildMember, PermissionFlagsBits, EmbedBuilder, ActionRowBuilder, ButtonBuilder, ButtonStyle } from 'discord.js';
import { randomUUID } from 'crypto';
// Fallback for older Node.js versions
const generateUUID = (): string => {
  try {
    return generateUUID();
  } catch (error) {
    // Fallback UUID generation for older Node.js
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
      const r = Math.random() * 16 | 0;
      const v = c === 'x' ? r : (r & 0x3 | 0x8);
      return v.toString(16);
    });
  }
};
import Poll, { IPoll, IPollVote } from '../../models/Poll';
import { requireFeature } from '../../config/features';
import { ValidationError, DatabaseError } from '../../utils/errorHandler';
import { IApplicationContext, ILogger, IService } from '../../core/interfaces';

/**
 * Poll Configuration Interface
 */
export interface PollConfig {
  title: string;
  description: string;
  options: string[];
  eligibleVoterRoles: string[];
  guildId: string;
  channelId: string;
  createdBy: string;
}

/**
 * Poll Service - Rebuilt following SuggestionService patterns
 * Handles all poll-related operations with robust permission validation
 */
export class PollService implements IService {
  public readonly name = 'PollService';
  private logger: ILogger;

  constructor(private app: IApplicationContext) {
    this.logger = app.logger;
    this.logger.info('[PollService] Poll service initialized with new architecture');
    // REBUILT POLL SERVICE DEBUG
    console.log('🎯 REBUILT POLL SERVICE ACTIVE - New permission validation system loaded');
    console.log('📊 PollService constructor called with new architecture');
    console.log('🔧 This message confirms the rebuilt PollService is being used');
    console.log('🎯 REBUILT POLL SERVICE ACTIVE - New permission validation system loaded');
  }

  async initialize(): Promise<void> {
    this.logger.info('[PollService] Service initialized');
  }

  async shutdown(): Promise<void> {
    this.logger.info('[PollService] Service shutdown');
  }

  /**
   * Create a new poll with comprehensive validation
   */
  @requireFeature('ECONOMY_SYSTEM')
  async createPoll(config: PollConfig, messageId: string): Promise<IPoll> {
    try {
      this.logger.info('[PollService] Creating new poll', {
        title: config.title,
        guildId: config.guildId,
        createdBy: config.createdBy,
        optionCount: config.options.length,
        eligibleRoleCount: config.eligibleVoterRoles.length
      });

      // Validate configuration
      this.validatePollConfig(config);

      // Create poll document
      const pollData = {
        pollId: generateUUID(),
        guildId: config.guildId,
        channelId: config.channelId,
        messageId: messageId,
        title: config.title,
        description: config.description,
        createdBy: config.createdBy,
        eligibleVoterRoles: config.eligibleVoterRoles,
        options: config.options,
        votes: [],
        totalVotes: 0,
        totalVoteWeight: 0,
        status: 'ACTIVE' as const,
        createdAt: new Date(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
      };

      const poll = new Poll(pollData);
      await poll.save();

      this.logger.info('[PollService] Poll created successfully', {
        pollId: poll.pollId,
        guildId: poll.guildId,
        messageId: poll.messageId
      });

      return poll;
    } catch (error) {
      this.logger.error('[PollService] Failed to create poll', {
        error: error instanceof Error ? error.message : 'Unknown error',
        config
      });
      
      if (error instanceof ValidationError) {
        throw error;
      }
      
      throw new DatabaseError('Failed to create poll', error as Error);
    }
  }

  /**
   * Get poll by ID with comprehensive error handling
   * FIXED: Removed .lean() to preserve Mongoose document methods like getVoteResults()
   */
  @requireFeature('ECONOMY_SYSTEM')
  async getPoll(pollId: string): Promise<IPoll | null> {
    try {
      const poll = await Poll.findOne({ pollId });

      if (!poll) {
        this.logger.debug('[PollService] Poll not found', { pollId });
        return null;
      }

      this.logger.debug('[PollService] Poll retrieved', {
        pollId: poll.pollId,
        status: poll.status,
        voteCount: poll.totalVotes
      });

      return poll;
    } catch (error) {
      this.logger.error('[PollService] Failed to get poll', {
        error: error instanceof Error ? error.message : 'Unknown error',
        pollId
      });
      throw new DatabaseError('Failed to retrieve poll', error as Error);
    }
  }

  /**
   * Check if user can vote in poll - Rebuilt with robust validation
   */
  @requireFeature('ECONOMY_SYSTEM')
  async canUserVote(pollId: string, userId: string, member: GuildMember): Promise<boolean> {
    try {
      this.logger.debug('[PollService] Checking vote permissions', {
        pollId,
        userId,
        guildId: member.guild.id
      });

      // Get poll first
      const poll = await this.getPoll(pollId);
      if (!poll || poll.status !== 'ACTIVE') {
        this.logger.debug('[PollService] Poll not found or not active', {
          pollId,
          status: poll?.status
        });
        return false;
      }



      // Validate member object
      if (!member || !member.roles || !member.roles.cache) {
        this.logger.error('[PollService] Invalid member object', {
          userId,
          pollId,
          hasMember: !!member,
          hasRoles: !!(member?.roles),
          hasCache: !!(member?.roles?.cache)
        });
        return false;
      }

      // Ensure member is from the same guild
      if (member.guild.id !== poll.guildId) {
        this.logger.error('[PollService] Guild mismatch', {
          userId,
          pollId,
          memberGuildId: member.guild.id,
          pollGuildId: poll.guildId
        });
        return false;
      }

      // Get user's role IDs
      const userRoleIds = Array.from(member.roles.cache.keys());
      
      // Validate and sanitize eligible voter roles (NEW SYSTEM)
      const sanitizedEligibleRoles = poll.eligibleVoterRoles
        .map(roleId => roleId.trim())
        .filter(roleId => /^\d{17,20}$/.test(roleId));
      const validEligibleRoles = sanitizedEligibleRoles;

      if (validEligibleRoles.length === 0) {
        this.logger.error('[PollService] No valid eligible voter roles', {
          pollId,
          originalRoles: poll.eligibleVoterRoles,
          validRoles: validEligibleRoles
        });
        return false;
      }

      // Check if user has any eligible role
      const hasEligibleRole = validEligibleRoles.some(roleId =>
        member.roles.cache.has(roleId)
      );

      // Debug logging for troubleshooting
      const userRoleNames = member.roles.cache.map ?
        member.roles.cache.map(r => r.name) :
        Array.from(member.roles.cache.values()).map(r => r.name);

      this.logger.debug('[PollService] Role validation details', {
        pollId,
        userId,
        guildId: member.guild.id,
        eligibleRoles: validEligibleRoles,
        userRoleIds,
        userRoleNames,
        hasEligibleRole
      });

      this.logger.debug('[PollService] Vote permission check result', {
        pollId,
        userId,
        hasEligibleRole,
        eligibleRoles: validEligibleRoles,
        userRoles: userRoleIds
      });

      return hasEligibleRole;
    } catch (error) {
      this.logger.error('[PollService] Error checking vote permissions', {
        error: error instanceof Error ? error.message : 'Unknown error',
        pollId,
        userId
      });
      return false;
    }
  }

  /**
   * Check if user can end poll - Rebuilt with robust validation
   */
  @requireFeature('ECONOMY_SYSTEM')
  async canUserEndPoll(pollId: string, userId: string, member: GuildMember): Promise<boolean> {
    try {
      this.logger.debug('[PollService] Checking end poll permissions', {
        pollId,
        userId,
        guildId: member.guild.id
      });

      // Get poll first
      const poll = await this.getPoll(pollId);
      if (!poll) {
        this.logger.debug('[PollService] Poll not found for end check', { pollId });
        return false;
      }

      // Debug logging
      this.logger.debug('[PollService] End permission debug', {
        pollId,
        userId,
        hasAdminPerms: member.permissions.has(PermissionFlagsBits.Administrator),
        isCreator: poll.createdBy === userId
      });

      // Validate member object
      if (!member || !member.permissions) {
        this.logger.error('[PollService] Invalid member object for end poll', {
          userId,
          pollId,
          hasMember: !!member,
          hasPermissions: !!(member?.permissions)
        });
        return false;
      }

      // Ensure member is from the same guild
      if (member.guild.id !== poll.guildId) {
        this.logger.error('[PollService] Guild mismatch for end poll', {
          userId,
          pollId,
          memberGuildId: member.guild.id,
          pollGuildId: poll.guildId
        });
        return false;
      }

      // Check if user is the poll creator
      const isCreator = poll.createdBy === userId;

      // Check if user has admin permissions
      const hasAdminPermissions = member.permissions.has(PermissionFlagsBits.Administrator);
      const hasManageMessages = member.permissions.has(PermissionFlagsBits.ManageMessages);

      const canEnd = isCreator || hasAdminPermissions || hasManageMessages;

      // Comprehensive debug logging
      console.log('=== POLL END PERMISSION CHECK (NEW SYSTEM) ===');
      console.log('Poll ID:', pollId);
      console.log('User ID:', userId);
      console.log('Poll Creator:', poll.createdBy);
      console.log('Is Creator:', isCreator);
      console.log('Has Administrator:', hasAdminPermissions);
      console.log('Has Manage Messages:', hasManageMessages);
      console.log('Can End Poll:', canEnd);
      console.log('=== END ADMIN CHECK ===');

      this.logger.debug('[PollService] End poll permission check result', {
        pollId,
        userId,
        isCreator,
        hasAdminPermissions,
        hasManageMessages,
        canEnd
      });

      return canEnd;
    } catch (error) {
      this.logger.error('[PollService] Error checking end poll permissions', {
        error: error instanceof Error ? error.message : 'Unknown error',
        pollId,
        userId
      });
      return false;
    }
  }

  /**
   * Cast vote in poll with comprehensive validation
   */
  @requireFeature('ECONOMY_SYSTEM')
  async castVote(pollId: string, userId: string, optionIndex: number, member: GuildMember): Promise<void> {
    try {
      this.logger.info('[PollService] Casting vote', {
        pollId,
        userId,
        optionIndex,
        guildId: member.guild.id
      });

      // Check if user can vote
      const canVote = await this.canUserVote(pollId, userId, member);
      if (!canVote) {
        throw new ValidationError('You do not have permission to vote in this poll');
      }

      // Get poll
      const poll = await Poll.findOne({ pollId });
      if (!poll) {
        throw new ValidationError('Poll not found');
      }

      // Validate option index
      if (optionIndex < 0 || optionIndex >= poll.options.length) {
        throw new ValidationError('Invalid poll option selected');
      }

      // Get user's coin balance for vote weight (using EconomyService)
      let userBalance = 0;
      try {
        const economyService = this.app.getService('EconomyService') as any;
        if (economyService && typeof economyService.getBalance === 'function') {
          userBalance = await economyService.getBalance(userId, member.guild.id) || 0;
        }
      } catch (error) {
        this.logger.warn('[PollService] Could not get user balance, using default weight', { userId, error });
      }

      // Create vote
      const vote: IPollVote = {
        userId,
        optionIndex,
        voteWeight: Math.max(1, userBalance), // Minimum weight of 1
        voterDisplayName: member.displayName,
        voterUsername: member.user.username,
        timestamp: new Date()
      };

      // Add vote using model method
      poll.addVote(vote);
      await poll.save();

      this.logger.info('[PollService] Vote cast successfully', {
        pollId,
        userId,
        optionIndex,
        voteWeight: vote.voteWeight,
        totalVotes: poll.totalVotes
      });
    } catch (error) {
      this.logger.error('[PollService] Failed to cast vote', {
        error: error instanceof Error ? error.message : 'Unknown error',
        pollId,
        userId,
        optionIndex
      });

      if (error instanceof ValidationError) {
        throw error;
      }

      throw new DatabaseError('Failed to cast vote', error as Error);
    }
  }

  /**
   * End poll with comprehensive validation
   */
  @requireFeature('ECONOMY_SYSTEM')
  async endPoll(pollId: string, userId: string, member: GuildMember): Promise<IPoll> {
    try {
      this.logger.info('[PollService] Ending poll', {
        pollId,
        userId,
        guildId: member.guild.id
      });

      // Check if user can end poll
      const canEnd = await this.canUserEndPoll(pollId, userId, member);
      if (!canEnd) {
        throw new ValidationError('You do not have permission to end this poll');
      }

      // Get and end poll
      const poll = await Poll.findOne({ pollId });
      if (!poll) {
        throw new ValidationError('Poll not found');
      }

      if (poll.status !== 'ACTIVE') {
        throw new ValidationError('Poll is not active');
      }

      // End poll using model method
      poll.endPoll(userId);
      await poll.save();

      this.logger.info('[PollService] Poll ended successfully', {
        pollId,
        endedBy: userId,
        totalVotes: poll.totalVotes,
        totalVoteWeight: poll.totalVoteWeight
      });

      return poll;
    } catch (error) {
      this.logger.error('[PollService] Failed to end poll', {
        error: error instanceof Error ? error.message : 'Unknown error',
        pollId,
        userId
      });

      if (error instanceof ValidationError) {
        throw error;
      }

      throw new DatabaseError('Failed to end poll', error as Error);
    }
  }

  /**
   * Create poll embed with voting buttons
   */
  createPollEmbed(poll: IPoll): { embed: EmbedBuilder; components: ActionRowBuilder<ButtonBuilder>[] } {
    const results = poll.getVoteResults();

    // Create embed
    const embed = new EmbedBuilder()
      .setTitle(`📊 ${poll.title}`)
      .setDescription(poll.description)
      .setColor(poll.status === 'ACTIVE' ? 0x00ff00 : 0xff0000)
      .setTimestamp(poll.createdAt);

    // Add poll options with results - Split into multiple fields if needed to respect Discord's 1024 char limit
    const maxFieldLength = 1000; // Leave some buffer under Discord's 1024 limit
    const optionFields: { name: string; value: string; inline: boolean }[] = [];

    let currentFieldText = '';
    let currentFieldIndex = 1;

    poll.options.forEach((option, index) => {
      const result = results[index];
      const bar = this.createProgressBar(result.percentage);
      const optionText = `**${index + 1}.** ${option}\n${bar} ${result.votes} votes (${result.percentage.toFixed(1)}%) | 💰 ${result.weight} coins (${result.weightPercentage.toFixed(1)}%)\n\n`;

      // Check if adding this option would exceed the field limit
      if (currentFieldText.length + optionText.length > maxFieldLength && currentFieldText.length > 0) {
        // Add current field and start a new one
        optionFields.push({
          name: optionFields.length === 0 ? 'Poll Options' : `Poll Options (continued ${currentFieldIndex})`,
          value: currentFieldText.trim(),
          inline: false
        });
        currentFieldText = optionText;
        currentFieldIndex++;
      } else {
        currentFieldText += optionText;
      }
    });

    // Add the final field
    if (currentFieldText.length > 0) {
      optionFields.push({
        name: optionFields.length === 0 ? 'Poll Options' : `Poll Options (continued ${currentFieldIndex})`,
        value: currentFieldText.trim(),
        inline: false
      });
    }

    // Add all option fields
    embed.addFields(optionFields);

    // Add summary fields
    embed.addFields([
      { name: 'Total Votes', value: `${poll.totalVotes}`, inline: true },
      { name: 'Total Weight', value: `${poll.totalVoteWeight} coins`, inline: true },
      { name: 'Status', value: poll.status, inline: true }
    ]);

    // Create voting buttons
    const components: ActionRowBuilder<ButtonBuilder>[] = [];

    if (poll.status === 'ACTIVE') {
      // Discord limits: 5 buttons per row, 5 rows max
      // We can fit up to 20 voting buttons (4 rows of 5) + 1 row for end button
      const maxVotingButtons = Math.min(poll.options.length, 20);
      const buttonsPerRow = 5;
      const maxVotingRows = 4; // Reserve 1 row for end button

      // Create voting button rows
      for (let row = 0; row < maxVotingRows && row * buttonsPerRow < maxVotingButtons; row++) {
        const startIndex = row * buttonsPerRow;
        const endIndex = Math.min(startIndex + buttonsPerRow, maxVotingButtons);

        const rowButtons = poll.options.slice(startIndex, endIndex).map((option, index) => {
          const optionIndex = startIndex + index;
          return new ButtonBuilder()
            .setCustomId(`poll_vote_${poll.pollId}_${optionIndex}`)
            .setLabel(`${optionIndex + 1}. ${option.substring(0, 18)}${option.length > 18 ? '...' : ''}`)
            .setStyle(ButtonStyle.Primary);
        });

        if (rowButtons.length > 0) {
          components.push(new ActionRowBuilder<ButtonBuilder>().addComponents(rowButtons));
        }
      }

      // Add end poll button in its own row
      const endButton = new ButtonBuilder()
        .setCustomId(`poll_end_${poll.pollId}`)
        .setLabel('End Poll')
        .setStyle(ButtonStyle.Danger);

      components.push(new ActionRowBuilder<ButtonBuilder>().addComponents(endButton));
    }

    return { embed, components };
  }

  /**
   * Create progress bar for vote visualization
   */
  private createProgressBar(percentage: number): string {
    const barLength = 10;
    const filledLength = Math.round((percentage / 100) * barLength);
    const emptyLength = barLength - filledLength;

    return '█'.repeat(filledLength) + '░'.repeat(emptyLength);
  }

  /**
   * Validate poll configuration with comprehensive checks
   */
  private validatePollConfig(config: PollConfig): void {
    // Title validation
    if (!config.title || config.title.trim().length === 0) {
      throw new ValidationError('Poll title is required');
    }
    if (config.title.length > 256) {
      throw new ValidationError('Poll title cannot exceed 256 characters');
    }

    // Description validation
    if (!config.description || config.description.trim().length === 0) {
      throw new ValidationError('Poll description is required');
    }
    if (config.description.length > 1024) {
      throw new ValidationError('Poll description cannot exceed 1024 characters');
    }

    // Options validation
    if (!config.options || config.options.length < 2) {
      throw new ValidationError('At least 2 poll options are required');
    }
    if (config.options.length > 20) {
      throw new ValidationError('Maximum 20 poll options allowed');
    }

    // Validate each option
    config.options.forEach((option, index) => {
      if (!option || option.trim().length === 0) {
        throw new ValidationError(`Poll option ${index + 1} cannot be empty`);
      }
      if (option.length > 100) {
        throw new ValidationError(`Poll option ${index + 1} cannot exceed 100 characters`);
      }
    });

    // Eligible voter roles validation
    if (!config.eligibleVoterRoles || config.eligibleVoterRoles.length === 0) {
      throw new ValidationError('At least one eligible voter role is required');
    }

    // Validate role IDs format
    config.eligibleVoterRoles.forEach((roleId, index) => {
      const trimmedRoleId = roleId.trim();
      if (!/^\d{17,20}$/.test(trimmedRoleId)) {
        throw new ValidationError(`Eligible voter role ${index + 1} has invalid format: ${roleId}. Must be a valid Discord snowflake.`);
      }
    });

    // Discord ID validations
    if (!/^\d{17,20}$/.test(config.guildId)) {
      throw new ValidationError('Invalid guild ID format');
    }
    if (!/^\d{17,20}$/.test(config.channelId)) {
      throw new ValidationError('Invalid channel ID format');
    }
    if (!/^\d{17,20}$/.test(config.createdBy)) {
      throw new ValidationError('Invalid creator ID format');
    }
  }
}
