"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const embedBuilder_1 = require("../utils/embedBuilder");
const LeaderboardManager_1 = require("../services/economy/managers/LeaderboardManager");
const logger_1 = require("../core/logger");
const logger = (0, logger_1.createLogger)('LeaderboardCommand');
module.exports = {
    data: new discord_js_1.SlashCommandBuilder()
        .setName('leaderboard')
        .setDescription('Show the top users by balance')
        .addIntegerOption(option => option.setName('page')
        .setDescription('Page number to view (default: 1)')
        .setMinValue(1)
        .setRequired(false)),
    execute: (0, errorHandler_1.withErrorHandler)(async (interaction) => {
        try {
            const guildId = interaction.guild?.id;
            if (!guildId) {
                throw new Error('This command can only be used in a server');
            }
            const requestedPage = interaction.options.getInteger('page') || 1;
            const leaderboardManager = new LeaderboardManager_1.LeaderboardManager(logger);
            const ENTRIES_PER_PAGE = 12;
            const leaderboardData = await leaderboardManager.getPaginatedLeaderboard(guildId, requestedPage, ENTRIES_PER_PAGE);
            const stats = await leaderboardManager.getLeaderboardStats(guildId);
            if (leaderboardData.entries.length === 0) {
                const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Leaderboard');
                embed.setDescription(`${embedBuilder_1.EMOJIS.MISC.MAGNIFYING} No users found yet!\n\nBe the first to earn some coins and claim the top spot!`)
                    .setColor(embedBuilder_1.COLORS.INFO);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            const currentMemberEntries = [];
            for (const user of leaderboardData.entries) {
                try {
                    if (!interaction.guild) {
                        logger.warn('No guild context available');
                        continue;
                    }
                    let guildMember;
                    try {
                        guildMember = interaction.guild.members.cache.get(user.discordId);
                        if (!guildMember) {
                            guildMember = await interaction.guild.members.fetch(user.discordId);
                        }
                    }
                    catch (fetchError) {
                        continue;
                    }
                    const displayName = guildMember.displayName || guildMember.user.username;
                    let positionEmoji = '';
                    if (user.rank === 1)
                        positionEmoji = '🥇';
                    else if (user.rank === 2)
                        positionEmoji = '🥈';
                    else if (user.rank === 3)
                        positionEmoji = '🥉';
                    else
                        positionEmoji = `${embedBuilder_1.EMOJIS.ROLES.MEDAL}`;
                    const formattedBalance = await (0, embedBuilder_1.formatServerCoins)(guildId, user.balance);
                    currentMemberEntries.push(`${positionEmoji} **#${user.rank}** ${displayName} — ${formattedBalance}`);
                }
                catch (error) {
                    logger.error(`Error processing user ${user.discordId}:`, error);
                    continue;
                }
            }
            if (currentMemberEntries.length === 0) {
                const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Leaderboard');
                embed.setDescription(`${embedBuilder_1.EMOJIS.MISC.MAGNIFYING} No current members found on the leaderboard!\n\nBe the first to earn some coins and claim the top spot!`)
                    .setColor(embedBuilder_1.COLORS.INFO);
                await interaction.reply({
                    embeds: [embed],
                    ephemeral: true
                });
                return;
            }
            const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Leaderboard');
            embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.TROPHY} **Top Players (Page ${leaderboardData.currentPage} of ${leaderboardData.totalPages})**\n\n` +
                currentMemberEntries.join('\n') +
                `\n\n${embedBuilder_1.EMOJIS.ECONOMY.SPARKLES} *Keep earning to climb higher!*`);
            const formattedTotalEconomy = await (0, embedBuilder_1.formatServerCoins)(guildId, stats.totalEconomyValue);
            const formattedAverage = await (0, embedBuilder_1.formatServerCoins)(guildId, stats.averageBalance);
            const formattedMedian = await (0, embedBuilder_1.formatServerCoins)(guildId, stats.medianBalance);
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.CHART} Server Statistics`,
                value: `**Total Players:** ${stats.totalUsers}\n**Total Economy:** ${formattedTotalEconomy}\n**Average Balance:** ${formattedAverage}`,
                inline: true
            }, {
                name: `${embedBuilder_1.EMOJIS.MISC.CALCULATOR} Balance Info`,
                value: `**Median Balance:** ${formattedMedian}\n**Highest Balance:** ${await (0, embedBuilder_1.formatServerCoins)(guildId, stats.maxBalance)}\n**Page:** ${leaderboardData.currentPage}/${leaderboardData.totalPages}`,
                inline: true
            });
            embed.addFields({
                name: `${embedBuilder_1.EMOJIS.MISC.CALENDAR} Last Updated`,
                value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
                inline: true
            });
            embed.setFooter({
                text: 'Use /balance to check your current position!'
            });
            const components = [];
            if (leaderboardData.totalPages > 1) {
                const navButtons = (0, embedBuilder_1.createNavigationButtons)(leaderboardData.currentPage, leaderboardData.totalPages);
                navButtons.components.forEach(button => {
                    const currentId = button.data.custom_id;
                    if (currentId?.startsWith('nav_')) {
                        const action = currentId.replace('nav_', '');
                        button.setCustomId(`leaderboard_${action}_${guildId}`);
                    }
                });
                components.push(navButtons);
            }
            await interaction.reply({
                embeds: [embed],
                components: components,
                ephemeral: false
            });
        }
        catch (error) {
            if (error instanceof Error) {
                throw new errorHandler_1.DatabaseError(error.message);
            }
            throw new errorHandler_1.DatabaseError('Failed to fetch leaderboard data.');
        }
    })
};
