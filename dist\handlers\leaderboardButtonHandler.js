"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderboardButtonHandler = void 0;
const discord_js_1 = require("discord.js");
const errorHandler_1 = require("../utils/errorHandler");
const logger_1 = require("../core/logger");
const LeaderboardManager_1 = require("../services/economy/managers/LeaderboardManager");
const embedBuilder_1 = require("../utils/embedBuilder");
class LeaderboardButtonHandler {
    constructor(app) {
        this.leaderboardStates = new Map();
        this.STATE_TIMEOUT = 15 * 60 * 1000;
        this.ENTRIES_PER_PAGE = 12;
        this.app = app;
        this.logger = (0, logger_1.createLogger)('LeaderboardButtonHandler');
        this.leaderboardManager = new LeaderboardManager_1.LeaderboardManager(this.logger);
        setInterval(() => this.cleanupExpiredStates(), 5 * 60 * 1000);
    }
    async handleLeaderboardButton(interaction, customId) {
        try {
            const parts = customId.split('_');
            if (parts.length < 3) {
                throw new errorHandler_1.ValidationError('Invalid leaderboard button format');
            }
            const action = parts[1];
            const guildId = parts[2];
            const requestedPage = parts.length > 3 ? parseInt(parts[3]) : undefined;
            this.logger.info(`Handling leaderboard button: ${action}`, {
                guildId,
                userId: interaction.user.id,
                requestedPage
            });
            if (!interaction.guild || interaction.guild.id !== guildId) {
                throw new errorHandler_1.ValidationError('Invalid guild context for leaderboard');
            }
            switch (action) {
                case 'first':
                    await this.handlePageNavigation(interaction, guildId, 1);
                    break;
                case 'prev':
                    await this.handlePreviousPage(interaction, guildId);
                    break;
                case 'next':
                    await this.handleNextPage(interaction, guildId);
                    break;
                case 'last':
                    await this.handleLastPage(interaction, guildId);
                    break;
                case 'refresh':
                    await this.handleRefresh(interaction, guildId);
                    break;
                default:
                    throw new errorHandler_1.ValidationError(`Unknown leaderboard action: ${action}`);
            }
        }
        catch (error) {
            this.logger.error('Error handling leaderboard button', {
                error,
                customId,
                userId: interaction.user.id
            });
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: `${embedBuilder_1.EMOJIS.ERROR.GENERAL} An error occurred while updating the leaderboard. Please try again.`,
                    ephemeral: true
                });
            }
        }
    }
    async handlePageNavigation(interaction, guildId, targetPage) {
        const stateKey = `${guildId}_${interaction.user.id}`;
        const state = this.leaderboardStates.get(stateKey) || {
            guildId,
            userId: interaction.user.id,
            currentPage: 1,
            entriesPerPage: this.ENTRIES_PER_PAGE,
            lastInteraction: Date.now()
        };
        state.currentPage = targetPage;
        state.lastInteraction = Date.now();
        this.leaderboardStates.set(stateKey, state);
        await this.updateLeaderboardMessage(interaction, guildId, state);
    }
    async handlePreviousPage(interaction, guildId) {
        const stateKey = `${guildId}_${interaction.user.id}`;
        const state = this.leaderboardStates.get(stateKey);
        if (!state) {
            await this.handlePageNavigation(interaction, guildId, 1);
            return;
        }
        const newPage = Math.max(1, state.currentPage - 1);
        await this.handlePageNavigation(interaction, guildId, newPage);
    }
    async handleNextPage(interaction, guildId) {
        const stateKey = `${guildId}_${interaction.user.id}`;
        const state = this.leaderboardStates.get(stateKey);
        if (!state) {
            await this.handlePageNavigation(interaction, guildId, 1);
            return;
        }
        const leaderboardData = await this.leaderboardManager.getPaginatedLeaderboard(guildId, state.currentPage, state.entriesPerPage);
        const newPage = Math.min(leaderboardData.totalPages, state.currentPage + 1);
        await this.handlePageNavigation(interaction, guildId, newPage);
    }
    async handleLastPage(interaction, guildId) {
        const leaderboardData = await this.leaderboardManager.getPaginatedLeaderboard(guildId, 1, this.ENTRIES_PER_PAGE);
        await this.handlePageNavigation(interaction, guildId, leaderboardData.totalPages);
    }
    async handleRefresh(interaction, guildId) {
        const stateKey = `${guildId}_${interaction.user.id}`;
        const state = this.leaderboardStates.get(stateKey) || {
            guildId,
            userId: interaction.user.id,
            currentPage: 1,
            entriesPerPage: this.ENTRIES_PER_PAGE,
            lastInteraction: Date.now()
        };
        state.lastInteraction = Date.now();
        this.leaderboardStates.set(stateKey, state);
        await this.updateLeaderboardMessage(interaction, guildId, state);
    }
    async updateLeaderboardMessage(interaction, guildId, state) {
        try {
            const leaderboardData = await this.leaderboardManager.getPaginatedLeaderboard(guildId, state.currentPage, state.entriesPerPage);
            const stats = await this.leaderboardManager.getLeaderboardStats(guildId);
            const embed = await this.createLeaderboardEmbed(interaction, guildId, leaderboardData, stats);
            const buttons = this.createLeaderboardButtons(guildId, leaderboardData);
            await interaction.update({
                embeds: [embed],
                components: buttons
            });
        }
        catch (error) {
            this.logger.error('Failed to update leaderboard message', {
                error,
                guildId,
                userId: interaction.user.id
            });
            throw error;
        }
    }
    async createLeaderboardEmbed(interaction, guildId, leaderboardData, stats) {
        const embed = await (0, embedBuilder_1.createServerEconomyEmbed)(guildId, 'Leaderboard');
        if (leaderboardData.entries.length === 0) {
            embed.setDescription(`${embedBuilder_1.EMOJIS.MISC.MAGNIFYING} No users found yet!\n\nBe the first to earn some coins and claim the top spot!`)
                .setColor(embedBuilder_1.COLORS.INFO);
            return embed;
        }
        const entries = [];
        for (const user of leaderboardData.entries) {
            try {
                const guildMember = await interaction.guild?.members.fetch(user.discordId);
                if (!guildMember)
                    continue;
                const displayName = guildMember.displayName || guildMember.user.username;
                let positionEmoji = '';
                if (user.rank === 1)
                    positionEmoji = '🥇';
                else if (user.rank === 2)
                    positionEmoji = '🥈';
                else if (user.rank === 3)
                    positionEmoji = '🥉';
                else
                    positionEmoji = `${embedBuilder_1.EMOJIS.ROLES.MEDAL}`;
                const formattedBalance = await (0, embedBuilder_1.formatServerCoins)(guildId, user.balance);
                entries.push(`${positionEmoji} **#${user.rank}** ${displayName} — ${formattedBalance}`);
            }
            catch (error) {
                continue;
            }
        }
        embed.setDescription(`${embedBuilder_1.EMOJIS.SUCCESS.TROPHY} **Top Players (Page ${leaderboardData.currentPage} of ${leaderboardData.totalPages})**\n\n` +
            entries.join('\n') +
            `\n\n${embedBuilder_1.EMOJIS.ECONOMY.SPARKLES} *Keep earning to climb higher!*`);
        const formattedTotalEconomy = await (0, embedBuilder_1.formatServerCoins)(guildId, stats.totalEconomyValue);
        const formattedAverage = await (0, embedBuilder_1.formatServerCoins)(guildId, stats.averageBalance);
        const formattedMedian = await (0, embedBuilder_1.formatServerCoins)(guildId, stats.medianBalance);
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.CHART} Server Statistics`,
            value: `**Total Players:** ${stats.totalUsers}\n**Total Economy:** ${formattedTotalEconomy}\n**Average Balance:** ${formattedAverage}`,
            inline: true
        }, {
            name: `${embedBuilder_1.EMOJIS.MISC.CALCULATOR} Balance Info`,
            value: `**Median Balance:** ${formattedMedian}\n**Highest Balance:** ${await (0, embedBuilder_1.formatServerCoins)(guildId, stats.maxBalance)}\n**Page:** ${leaderboardData.currentPage}/${leaderboardData.totalPages}`,
            inline: true
        });
        embed.addFields({
            name: `${embedBuilder_1.EMOJIS.MISC.CALENDAR} Last Updated`,
            value: `<t:${Math.floor(Date.now() / 1000)}:R>`,
            inline: true
        });
        embed.setFooter({
            text: 'Use /balance to check your current position!'
        });
        return embed;
    }
    createLeaderboardButtons(guildId, leaderboardData) {
        const navButtons = (0, embedBuilder_1.createNavigationButtons)(leaderboardData.currentPage, leaderboardData.totalPages);
        navButtons.components.forEach(button => {
            const currentId = button.data.custom_id;
            if (currentId?.startsWith('nav_')) {
                const action = currentId.replace('nav_', '');
                button.setCustomId(`leaderboard_${action}_${guildId}`);
            }
        });
        const refreshButton = new discord_js_1.ButtonBuilder()
            .setCustomId(`leaderboard_refresh_${guildId}`)
            .setLabel('Refresh')
            .setEmoji('🔄')
            .setStyle(discord_js_1.ButtonStyle.Secondary);
        navButtons.addComponents(refreshButton);
        return [navButtons];
    }
    cleanupExpiredStates() {
        const now = Date.now();
        for (const [key, state] of this.leaderboardStates.entries()) {
            if (now - state.lastInteraction > this.STATE_TIMEOUT) {
                this.leaderboardStates.delete(key);
            }
        }
    }
    createInitialState(guildId, userId) {
        const state = {
            guildId,
            userId,
            currentPage: 1,
            entriesPerPage: this.ENTRIES_PER_PAGE,
            lastInteraction: Date.now()
        };
        const stateKey = `${guildId}_${userId}`;
        this.leaderboardStates.set(stateKey, state);
        return state;
    }
}
exports.LeaderboardButtonHandler = LeaderboardButtonHandler;
