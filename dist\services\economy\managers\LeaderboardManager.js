"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.LeaderboardManager = void 0;
const errorHandler_1 = require("../../../utils/errorHandler");
const features_1 = require("../../../config/features");
const User_1 = __importDefault(require("../../../models/User"));
class LeaderboardManager {
    constructor(logger) {
        this.logger = logger;
    }
    async getLeaderboard(guildId, limit = 10) {
        try {
            this.validateLeaderboardParams(limit);
            if (!guildId || typeof guildId !== 'string') {
                throw new errorHandler_1.DatabaseError('Guild ID is required for leaderboard queries');
            }
            const users = await User_1.default.find({ guildId })
                .sort({ balance: -1 })
                .limit(limit)
                .lean();
            const leaderboard = users.map((user, index) => ({
                discordId: user.discordId,
                balance: user.balance,
                rank: index + 1,
            }));
            this.logOperation('Leaderboard retrieved', {
                guildId,
                limit,
                entriesCount: leaderboard.length
            });
            return leaderboard;
        }
        catch (error) {
            this.handleError(error, { guildId, limit });
            throw new errorHandler_1.DatabaseError(`Failed to get leaderboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getUserRank(discordId, guildId) {
        try {
            if (!discordId || typeof discordId !== 'string') {
                throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
            }
            if (!guildId || typeof guildId !== 'string') {
                throw new errorHandler_1.DatabaseError('Guild ID is required for rank queries');
            }
            const trimmedDiscordId = discordId.trim();
            const user = await User_1.default.findOne({ discordId: trimmedDiscordId, guildId }).lean();
            if (!user) {
                throw new errorHandler_1.DatabaseError('User not found in this guild');
            }
            const higherBalanceCount = await User_1.default.countDocuments({
                guildId,
                balance: { $gt: user.balance }
            });
            const rank = higherBalanceCount + 1;
            this.logOperation('User rank calculated', {
                discordId: trimmedDiscordId,
                guildId,
                balance: user.balance,
                rank
            });
            return rank;
        }
        catch (error) {
            this.handleError(error, { discordId, guildId });
            throw new errorHandler_1.DatabaseError(`Failed to get user rank: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getTopUsers(guildId, limit = 10) {
        try {
            this.validateLeaderboardParams(limit);
            if (!guildId || typeof guildId !== 'string') {
                throw new errorHandler_1.DatabaseError('Guild ID is required for top users queries');
            }
            const users = await User_1.default.find({ guildId })
                .sort({ balance: -1 })
                .limit(limit)
                .select('discordId balance')
                .lean();
            const topUsers = users.map((user, index) => ({
                discordId: user.discordId,
                balance: user.balance,
                rank: index + 1,
            }));
            this.logOperation('Top users retrieved', {
                guildId,
                limit,
                usersCount: topUsers.length
            });
            return topUsers;
        }
        catch (error) {
            this.handleError(error, { guildId, limit });
            throw new errorHandler_1.DatabaseError(`Failed to get top users: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getLeaderboardAroundUser(discordId, guildId, range = 5) {
        try {
            if (!discordId || typeof discordId !== 'string') {
                throw new errorHandler_1.DatabaseError('Invalid Discord ID provided');
            }
            if (!guildId || typeof guildId !== 'string') {
                throw new errorHandler_1.DatabaseError('Guild ID is required for leaderboard queries');
            }
            const trimmedDiscordId = discordId.trim();
            const userRank = await this.getUserRank(trimmedDiscordId, guildId);
            const startRank = Math.max(1, userRank - range);
            const endRank = userRank + range;
            const skip = startRank - 1;
            const limit = endRank - startRank + 1;
            const users = await User_1.default.find({ guildId })
                .sort({ balance: -1 })
                .skip(skip)
                .limit(limit)
                .lean();
            const leaderboard = users.map((user, index) => ({
                discordId: user.discordId,
                balance: user.balance,
                rank: startRank + index,
            }));
            this.logOperation('Leaderboard around user retrieved', {
                discordId: trimmedDiscordId,
                userRank,
                range,
                entriesCount: leaderboard.length
            });
            return leaderboard;
        }
        catch (error) {
            this.handleError(error, { discordId, range });
            throw new errorHandler_1.DatabaseError(`Failed to get leaderboard around user: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getLeaderboardStats(guildId) {
        try {
            if (!guildId || typeof guildId !== 'string') {
                throw new errorHandler_1.DatabaseError('Guild ID is required for leaderboard stats');
            }
            const stats = await User_1.default.aggregate([
                { $match: { guildId } },
                {
                    $group: {
                        _id: null,
                        totalUsers: { $sum: 1 },
                        totalBalance: { $sum: '$balance' },
                        averageBalance: { $avg: '$balance' },
                        maxBalance: { $max: '$balance' },
                        minBalance: { $min: '$balance' }
                    }
                }
            ]);
            const medianStats = await User_1.default.aggregate([
                { $match: { guildId, balance: { $gt: 0 } } },
                { $sort: { balance: 1 } },
                {
                    $group: {
                        _id: null,
                        balances: { $push: '$balance' },
                        count: { $sum: 1 }
                    }
                },
                {
                    $project: {
                        median: {
                            $cond: {
                                if: { $eq: [{ $mod: ['$count', 2] }, 0] },
                                then: {
                                    $avg: [
                                        { $arrayElemAt: ['$balances', { $subtract: [{ $divide: ['$count', 2] }, 1] }] },
                                        { $arrayElemAt: ['$balances', { $divide: ['$count', 2] }] }
                                    ]
                                },
                                else: { $arrayElemAt: ['$balances', { $floor: { $divide: ['$count', 2] } }] }
                            }
                        }
                    }
                }
            ]);
            const basicResult = stats[0] || {
                totalUsers: 0,
                totalBalance: 0,
                averageBalance: 0,
                maxBalance: 0,
                minBalance: 0
            };
            const medianResult = medianStats[0]?.median || 0;
            const result = {
                totalUsers: basicResult.totalUsers,
                totalBalance: basicResult.totalBalance,
                averageBalance: Math.round(basicResult.averageBalance * 100) / 100,
                medianBalance: Math.round(medianResult * 100) / 100,
                maxBalance: basicResult.maxBalance,
                minBalance: basicResult.minBalance,
                totalEconomyValue: basicResult.totalBalance
            };
            this.logOperation('Guild leaderboard stats calculated', {
                guildId,
                ...result
            });
            return result;
        }
        catch (error) {
            this.handleError(error);
            throw new errorHandler_1.DatabaseError(`Failed to get guild leaderboard stats: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    async getPaginatedLeaderboard(guildId, page = 1, limit = 10) {
        try {
            this.validateLeaderboardParams(limit);
            if (!guildId || typeof guildId !== 'string') {
                throw new errorHandler_1.DatabaseError('Guild ID is required for leaderboard queries');
            }
            if (page < 1) {
                page = 1;
            }
            const skip = (page - 1) * limit;
            const totalUsers = await User_1.default.countDocuments({ guildId, balance: { $gt: 0 } });
            const totalPages = Math.ceil(totalUsers / limit);
            const users = await User_1.default.find({ guildId, balance: { $gt: 0 } })
                .sort({ balance: -1 })
                .skip(skip)
                .limit(limit)
                .lean();
            const entries = users.map((user, index) => ({
                discordId: user.discordId,
                balance: user.balance,
                rank: skip + index + 1,
            }));
            const result = {
                entries,
                totalUsers,
                totalPages,
                currentPage: page,
                hasNextPage: page < totalPages,
                hasPrevPage: page > 1
            };
            this.logOperation('Paginated leaderboard retrieved', {
                guildId,
                page,
                limit,
                totalUsers,
                totalPages,
                entriesCount: entries.length
            });
            return result;
        }
        catch (error) {
            this.handleError(error);
            throw new errorHandler_1.DatabaseError(`Failed to get paginated leaderboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }
    validateLeaderboardParams(limit) {
        if (typeof limit !== 'number' || limit < 1 || limit > 100) {
            throw new errorHandler_1.DatabaseError('Limit must be a number between 1 and 100');
        }
    }
    logOperation(operation, details) {
        this.logger.debug(`[LeaderboardManager] ${operation}`, details);
    }
    handleError(error, context) {
        this.logger.error('[LeaderboardManager] Error', {
            error: error instanceof Error ? {
                name: error.name,
                message: error.message,
                stack: error.stack,
            } : error,
            context,
        });
    }
}
exports.LeaderboardManager = LeaderboardManager;
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getLeaderboard", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getUserRank", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number]),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getTopUsers", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, Number]),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getLeaderboardAroundUser", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getLeaderboardStats", null);
__decorate([
    (0, features_1.requireFeature)('ECONOMY_SYSTEM'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", Promise)
], LeaderboardManager.prototype, "getPaginatedLeaderboard", null);
